<template>
	<view class="container">
		<view class="card">
			<view class="filter-section">
				<view class="filter-column">
					<!-- 能耗类型筛选 -->
					<view class="filter-item">
						<text class="filter-label">能耗类型：</text>
						<uni-data-select
							v-model="energyType"
							:localdata="energyTypeOptions"
							@change="onEnergyTypeChange"
							class="energy-select"
						/>
					</view>

					<!-- 日期选择区域 -->
					<view class="filter-item date-filter">
						<text class="filter-label">统计时间：</text>
						<view class="date-select-container">
							<view class="date-and-buttons-row">
								<view class="date-range-container">
									<DayPicker
										:value="dateRangeArray"
										@change="handleDateRangeChange"
										:clear-icon="true"
										:single-mode="false"
										:disable-future-dates="false"
										class="custom-date-picker"
									/>
								</view>
								<view class="button-group">
									<button type="primary" size="mini" class="quick-button" @click="setLastSevenDays">最近7天</button>
									<button type="primary" size="mini" class="quick-button" @click="setCurrentMonth">本月</button>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<view class="card">
			<view class="chart-container">
				<view v-if="!chartData.categories || chartData.categories.length === 0" class="no-data">
					暂无数据
				</view>
				<qiun-data-charts
					v-else
					type="column"
					:opts="chartOpts"
					:chartData="chartData"
					canvasId="energyChart"
				/>
			</view>
		</view>

		<view class="card">
			<view class="table-title">{{ energyTypeLabel }}能耗列表</view>
			<view class="table">
				<view class="table-header">
					<view class="th">统计时间</view>
					<view class="th">使用量</view>
					<view class="th">同比</view>
					<view class="th">环比</view>
					<view class="th">均摊量</view>
					<view class="th">总价</view>
				</view>
				<scroll-view scroll-y class="table-body" :style="{ height: tableHeight + 'px' }">
					<view v-for="(item, index) in paginatedTableData" :key="index" class="tr">
						<view class="td">{{ item.statisticsTime }}</view>
						<view class="td">{{ item.usage }}</view>
						<view class="td" :class="{'up': Number(item.onYear) > 0, 'down': Number(item.onYear) < 0}">
							{{ item.onYear }}%
						</view>
						<view class="td" :class="{'up': Number(item.chainRate) > 0, 'down': Number(item.chainRate) < 0}">
							{{ item.chainRate }}%
						</view>
						<view class="td">{{ item.shareUsage }}</view>
						<view class="td">¥{{ item.allAmt }}</view>
					</view>
				</scroll-view>
			</view>
			<view class="pagination">
				<uni-pagination
					:total="total"
					:pageSize="pageSize"
					:current="currentPage"
					@change="handleCurrentChange"
				/>
			</view>
		</view>
	</view>
</template>

<script>
import { qryStatisticsByType } from '@/api/HRP/weg';
import { getDayPeakValue } from '@/api/portal/statistics';
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue';

export default {
	components: {
		DayPicker
	},
	data() {
		return {
			energyTypeOptions: [
				{ value: 1, text: '用电' },
				{ value: 2, text: '用水' },
				{ value: 3, text: '用气' }
			],
			energyType: 1,
			deptId: -1,
			dateRange: [],
			dateRangeArray: [], // 用于 DayPicker 组件的日期范围数组
			chartData: {
				categories: [],
				series: []
			},
			tableData: [],
			currentPage: 1,
			pageSize: 10,
			total: 0,
			peakValue: {
				dayElectricityPeakValue: null,
				dayWaterPeakValue: null,
				dayGasPeakValue: null
			},
			chartOpts: {
				color: ['#409EFF', '#67C23A', '#E6A23C'],
				padding: [15, 15, 0, 15],
				legend: {
					show: true
				},
				xAxis: {
					disableGrid: true
				},
				yAxis: {
					gridType: 'dash',
					dashLength: 2
				},
				extra: {
					column: {
						width: 30
					}
				}
			},
			tableHeight: 400, // 表格高度
		}
	},
	computed: {
		energyTypeLabel() {
			const option = this.energyTypeOptions.find(item => item.value === this.energyType);
			return option ? option.text : '';
		},
		paginatedTableData() {
			const start = (this.currentPage - 1) * this.pageSize;
			const end = start + this.pageSize;
			return this.tableData.slice(start, end);
		}
	},
	onLoad() {
		this.setLastSevenDays();
		// 计算表格高度
		const systemInfo = uni.getSystemInfoSync();
		this.tableHeight = systemInfo.windowHeight * 0.5; // 设置为屏幕高度的50%
	},
	methods: {
		onEnergyTypeChange(value) {
			this.energyType = value;
			this.fetchData();
		},
		// 处理 DayPicker 组件的日期范围变化
		handleDateRangeChange(dateRange) {
			console.log('日期范围变化:', dateRange);
			this.dateRangeArray = dateRange || [];

			if (dateRange && dateRange.length === 2 && dateRange[0] && dateRange[1]) {
				this.dateRange = [dateRange[0], dateRange[1]];
				this.fetchData();
			}
		},
		setLastSevenDays() {
			const end = new Date();
			const start = new Date();
			start.setDate(end.getDate() - 6);

			const startDateStr = start.toISOString().split('T')[0];
			const endDateStr = end.toISOString().split('T')[0];

			this.dateRange = [startDateStr, endDateStr];
			this.dateRangeArray = [startDateStr, endDateStr]; // 同时更新 DayPicker 的值
			this.fetchData();
		},
		setCurrentMonth() {
			const now = new Date();
			const year = now.getFullYear();
			const month = String(now.getMonth() + 1).padStart(2, '0');

			const startDate = `${year}-${month}-01`;
			const endDate = now.toISOString().split('T')[0];

			this.dateRange = [startDate, endDate];
			this.dateRangeArray = [startDate, endDate]; // 同时更新 DayPicker 的值
			this.fetchData();
		},
		handleCurrentChange(e) {
			this.currentPage = e.current;
		},
		async fetchData() {
			if (!this.dateRange || this.dateRange.length !== 2) return;
			const params = {
				timeType: 2,
				startTime: this.dateRange[0],
				endTime: this.dateRange[1],
				typeId: null,
				orgId: this.deptId,
				qryType: this.energyType
			};
			
			try {
				const [res, peakRes] = await Promise.all([
					qryStatisticsByType(params),
					getDayPeakValue()
				]);
				
				if (peakRes && peakRes.code === 200 && peakRes.data) {
					this.peakValue = peakRes.data;
				}
				
				if (res.code === 200 && res.data && res.data.length > 0) {
					const dayMap = new Map();
					res.data.forEach(item => {
						const calcTime = String(item.calcTime);
						const year = calcTime.substring(0, 4);
						const month = calcTime.substring(4, 6);
						const day = calcTime.substring(6, 8);
						const statisticsTime = `${year}-${month}-${day}`;
						
						if (!dayMap.has(statisticsTime)) {
							dayMap.set(statisticsTime, {
								statisticsTime,
								usage: item.usage.toFixed(4),
								onYear: item.onYear !== null ? (item.onYear * 100).toFixed(2) : '-',
								chainRate: item.chainRate !== null ? (item.chainRate * 100).toFixed(2) : '-',
								area: item.area.toFixed(4),
								shareUsage: item.shareUsage !== null ? item.shareUsage.toFixed(4) : '-',
								unitAmt: item.unitAmt !== null ? item.unitAmt.toFixed(2) : '-',
								allAmt: item.allAmt !== null ? item.allAmt.toFixed(2) : '-'
							});
						}
					});
					
					this.tableData = Array.from(dayMap.values()).sort((a, b) => 
						new Date(a.statisticsTime) - new Date(b.statisticsTime)
					);
					this.total = this.tableData.length;
					this.currentPage = 1;
					
					// 更新图表数据
					this.updateChartData();
				} else {
					// 清空数据
					this.tableData = [];
					this.total = 0;
					this.currentPage = 1;
					this.chartData = {
						categories: [],
						series: []
					};
				}
			} catch (error) {
				uni.showToast({
					title: '获取数据失败',
					icon: 'none'
				});
				// 清空数据
				this.tableData = [];
				this.total = 0;
				this.currentPage = 1;
				this.chartData = {
					categories: [],
					series: []
				};
			}
		},
		updateChartData() {
			const categories = this.tableData.map(item => item.statisticsTime);
			const usageData = this.tableData.map(item => Number(item.usage));
			const onYearData = this.tableData.map(item => Number(item.onYear));
			const chainRateData = this.tableData.map(item => Number(item.chainRate));
			
			// 获取当前能耗类型的峰值
			let peak = 0;
			if (this.energyType === 1 && this.peakValue.dayElectricityPeakValue != null) 
				peak = this.peakValue.dayElectricityPeakValue;
			if (this.energyType === 2 && this.peakValue.dayWaterPeakValue != null) 
				peak = this.peakValue.dayWaterPeakValue;
			if (this.energyType === 3 && this.peakValue.dayGasPeakValue != null) 
				peak = this.peakValue.dayGasPeakValue;
			
			// 为超出峰值的数据设置不同的颜色
			const usageDataWithColor = usageData.map(value => ({
				value: value,
				color: value > peak ? '#ff4d4f' : '#409EFF'  // 超出峰值为红色，否则为蓝色
			}));
			
			this.chartData = {
				categories: categories,
				series: [
					{
						name: '使用量',
						data: usageDataWithColor,
						type: 'column'
					},
					{
						name: '同比',
						data: onYearData,
						type: 'line'
					},
					{
						name: '环比',
						data: chainRateData,
						type: 'line'
					}
				]
			};
			
			// 更新图表配置
			this.chartOpts = {
				...this.chartOpts,
				title: {
					name: `${this.energyTypeLabel}能耗图表`,
					fontSize: 16,
					color: '#333'
				},
				markLine: {
					data: [{
						yAxis: peak,
						lineStyle: {
							color: '#faad14',
							type: 'dashed',
							width: 2
						},
						label: {
							formatter: `峰值: ${peak}`,
							position: 'end',
							color: '#faad14'
						}
					}]
				}
			};
		}
	}
}
</script>

<style lang="scss">
/*
 * 医院日能耗峰值监控页面样式 - 美化版
 */

.container {
	padding: 24rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	min-height: 100vh;
	position: relative;

	&::before {
		content: '';
		position: fixed;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		background:
			radial-gradient(circle at 20% 20%, rgba(64, 158, 255, 0.03) 0%, transparent 50%),
			radial-gradient(circle at 80% 80%, rgba(102, 179, 255, 0.03) 0%, transparent 50%);
		pointer-events: none;
		z-index: -1;
	}

	/* 卡片通用样式 - 简单美化 */
	.card {
		background: white;
		border-radius: 16rpx;
		padding: 24rpx;
		margin-bottom: 20rpx;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
		border: 1rpx solid #f0f0f0;
	}

	/* 筛选区域样式 - 简单美化 */
	.filter-section {
		display: flex;
		flex-direction: column;
		gap: 16rpx;
		background: #fafbfc;
		border-radius: 12rpx;
		padding: 16rpx;

		/* 垂直筛选列样式 */
		.filter-column {
			display: flex;
			flex-direction: column;
			gap: 16rpx;

			/* 单个筛选项样式 - 简单美化 */
			.filter-item {
				display: flex;
				align-items: center;
				width: 100%;
				padding: 16rpx;
				background: white;
				border-radius: 10rpx;
				border: 1rpx solid #e9ecef;
				box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
				margin-bottom: 12rpx;

				/* 筛选标签 */
				.filter-label {
					min-width: 150rpx;
					font-size: 30rpx;
					color: #495057;
					font-weight: 600;
				}

				/* 能耗类型选择器 */
				.energy-select {
					width: 300rpx;
					height: 72rpx;
					line-height: 72rpx;
					border: 1rpx solid #e9ecef;
					border-radius: 10rpx;
					background: white;
					font-size: 28rpx;
					color: #495057;
					padding: 0 12rpx;
					box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
					transition: all 0.3s ease;

					&:focus {
						border-color: #409EFF;
						box-shadow: 0 0 0 3rpx rgba(64, 158, 255, 0.1);
					}
				}

				/* 日期筛选区域 - 简单美化 */
				&.date-filter {
					margin-top: 20rpx;
					padding: 20rpx;
					background: #fafbfc;
					border-radius: 12rpx;
					border: 1rpx solid #e9ecef;

					.filter-label {
						margin-bottom: 12rpx;
						color: #495057;
						font-weight: 600;
					}

					.date-select-container {
						flex: 1;
						display: flex;
						flex-direction: column;
						gap: 20rpx;

						/* 日期选择和快捷按钮同一排 */
						.date-and-buttons-row {
							display: flex;
							align-items: center;
							gap: 16rpx;
							flex-wrap: wrap;

							/* 日期范围选择器 */
							.date-range-container {
								flex: 1;
								min-width: 300rpx;
								display: flex;
								align-items: center;
								background: white;
								border-radius: 10rpx;
								padding: 12rpx;
								border: 1rpx solid #e9ecef;
								box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);

								.date-picker {
									flex: 1;
									border: none;
									border-radius: 6rpx;
									padding: 8rpx 12rpx;
									background: #f8f9fa;
									font-size: 26rpx;
									color: #495057;
								}

								.date-separator {
									margin: 0 12rpx;
									color: #6c757d;
									font-size: 24rpx;
									font-weight: 500;
								}
							}

							/* 快捷日期按钮组 */
							.button-group {
								display: flex;
								gap: 8rpx;
								flex-wrap: wrap;
								align-items: center;

								.quick-button {
									font-size: 22rpx;
									padding: 0 16rpx;
									height: 56rpx;
									line-height: 56rpx;
									margin: 0;
									background: linear-gradient(135deg, #409EFF 0%, #5dade2 100%);
									border: none;
									border-radius: 28rpx;
									color: white;
									font-weight: 500;
									box-shadow: 0 2rpx 6rpx rgba(64, 158, 255, 0.2);
									transition: all 0.3s ease;
									white-space: nowrap;

									&:hover {
										transform: translateY(-1rpx);
										box-shadow: 0 4rpx 10rpx rgba(64, 158, 255, 0.3);
									}
								}
							}

							/* 小屏幕响应式 */
							@media (max-width: 750rpx) {
								flex-direction: column;
								align-items: stretch;
								gap: 12rpx;

								.date-range-container {
									min-width: auto;
									width: 100%;
								}

								.button-group {
									justify-content: center;
									width: 100%;

									.quick-button {
										flex: 1;
										min-width: 80rpx;
										font-size: 20rpx;
										padding: 0 12rpx;
									}
								}
							}
						}
					}
				}
			}
		}
	}

	/* 自定义时间选择器样式 - 美化版 */
	.custom-date-picker {
		width: 100%;
		margin-bottom: 24rpx;
		border-radius: 12rpx;
		overflow: hidden;
		box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.06);
		border: 2rpx solid #e9ecef;
		background: white;
		transition: all 0.3s ease;

		&:hover {
			border-color: #409EFF;
			box-shadow: 0 6rpx 20rpx rgba(64, 158, 255, 0.15);
		}
	}
	/* 图表容器样式 */
	.chart-container {
		width: 100%;
		height: 800rpx;
		position: relative;
		margin: 0 auto;
		overflow: hidden;

		/* 无数据显示状态 */
		.no-data {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 32rpx;
			color: #999;
			background: rgba(255,255,255,0.8);
			z-index: 2;
		}
	}

	/* 表格标题样式 */
	.table-title {
		font-size: 32rpx;
		font-weight: bold;
		text-align: center;
		margin-bottom: 30rpx;
		color: #333;
	}

	/* 数据表格样式 */
	.table {
		width: 100%;
		background: #fff;
		border-radius: 8rpx;
		overflow: hidden;
		border: 1px solid #f0f0f0;

		/* 表头样式 */
		.table-header {
			display: flex;
			background-color: #f8f9fa;
			position: sticky;
			top: 0;
			z-index: 1;

			.th {
				flex: 1;
				padding: 24rpx 12rpx;
				text-align: center;
				font-weight: 500;
				font-size: 28rpx;
				color: #333;
				white-space: nowrap;
			}
		}

		/* 表格内容区域 */
		.table-body {
			.tr {
				display: flex;
				border-bottom: 1px solid #f0f0f0;
				transition: background-color 0.3s;

				&:active {
					background-color: #f5f5f5;
				}

				.td {
					flex: 1;
					padding: 24rpx 12rpx;
					text-align: center;
					font-size: 26rpx;
					color: #333;
					white-space: nowrap;

					/* 升降指标样式 */
					&.up {
						color: #f56c6c; /* 红色表示上升 */
					}

					&.down {
						color: #67c23a; /* 绿色表示下降 */
					}
				}
			}
		}
	}

	/* 分页控件样式 */
	.pagination {
		margin-top: 24rpx;
		display: flex;
		justify-content: center;
		padding: 20rpx 0;
	}

	/* 响应式设计 */
	@media (max-width: 750rpx) {
		.filter-item {
			flex-direction: column;
			align-items: flex-start;
			gap: 16rpx;

			.filter-label {
				min-width: auto;
				margin-bottom: 8rpx;
			}

			.energy-select {
				width: 100%;
			}
		}
	}
}
</style>
