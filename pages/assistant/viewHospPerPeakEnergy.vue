<template>
  <view class="container">
    <view class="card">
      <view class="filter-section">
        <!-- 能耗类型和统计周期垂直排列在同一列 -->
        <view class="filter-column">
          <view class="filter-item">
            <text class="filter-label">能耗类型：</text>
            <uni-data-select
                v-model="energyType"
                :localdata="energyTypeOptions"
                @change="onEnergyTypeChange"
                class="energy-select"
            />
          </view>
          <view class="filter-item">
            <text class="filter-label">统计周期：</text>
            <view class="segmented-control">
              <view
                  v-for="(item, index) in timePeriodOptions"
                  :key="index"
                  :class="['segment', { active: timePeriod === item.value }]"
                  @click="onTimePeriodChange(item.value)"
              >
                {{ item.text }}
              </view>
            </view>
          </view>

          <!-- 日期选择区域 -->
          <view class="filter-item date-filter">
            <text class="filter-label">统计时间：</text>
            <view class="date-select-container">
              <!-- 日期范围选择 -->
              <template v-if="timePeriod === 1">
                <view class="date-range-container">
                  <DayPicker
                      :value="dateRangeArray"
                      @change="handleDateRangeChange"
                      :clear-icon="true"
                      :single-mode="false"
                      :disable-future-dates="false"
                      class="custom-date-picker"
                  />
                </view>
                <view class="button-group">
                  <button type="primary" size="mini" class="quick-button" @click="setLastSevenDays">最近7天</button>
                  <button type="primary" size="mini" class="quick-button" @click="setCurrentMonth">本月</button>
                </view>
              </template>

              <!-- 月份选择 -->
              <template v-else-if="timePeriod === 2">
                <view class="date-range-container">
                  <MonthPicker
                      :value="monthRangeArray"
                      @change="handleMonthRangeChange"
                      :clear-icon="true"
                      :single-mode="false"
                      :disable-future-dates="false"
                      class="custom-date-picker"
                  />
                </view>
                <view class="button-group">
                  <button type="primary" size="mini" class="quick-button" @click="setCurrentMonthOnly">本月</button>
                  <button type="primary" size="mini" class="quick-button" @click="setLastThreeMonths">近三月</button>
                  <button type="primary" size="mini" class="quick-button" @click="setLastSixMonths">近六月</button>
                </view>
              </template>

              <!-- 年份选择 -->
              <template v-else>
                <view class="date-range-container">
                  <YearPicker
                      :value="yearRangeArray"
                      @change="handleYearRangeChange"
                      :clear-icon="true"
                      :single-mode="false"
                      :min-year="2000"
                      :max-year="2050"
                      :disable-future-dates="false"
                      class="custom-date-picker"
                  />
                </view>
                <view class="button-group">
                  <button type="primary" size="mini" class="quick-button" @click="setCurrentYear">今年</button>
                  <button type="primary" size="mini" class="quick-button" @click="setLastYear">前一年</button>
                  <button type="primary" size="mini" class="quick-button" @click="setLastThreeYears">近三年</button>
                </view>
              </template>
            </view>
          </view>
        </view>
      </view>
    </view>

    <view class="card">
      <view class="chart-container" :style="{ height: chartHeight + 'rpx' }">
        <view v-if="!chartData.categories || chartData.categories.length === 0" class="no-data">
          暂无数据
        </view>
        <qiun-data-charts
            v-else
            type="mix"
            :opts="chartOpts"
            :chartData="chartData"
            canvasId="energyChart"
            @error="onChartError"
            @complete="onChartInit"
            @itemTap="onItemTap"
            :ontouch="true"
            :tooltipShow="true"
            :disableScroll="false"
        />
      </view>
    </view>

    <view class="card">
      <view class="table-title">{{ energyTypeLabel }}均摊用量列表</view>
      <view class="table">
        <view class="table-header">
          <view class="th">时间</view>
          <view class="th">均摊量</view>
          <view class="th">使用量</view>
<!--          <view class="th">同比</view>-->
<!--          <view class="th">环比</view>-->
          <view class="th">单价</view>
          <view class="th">总价</view>
        </view>
        <scroll-view scroll-y class="table-body" :style="{ height: tableHeight + 'px' }">
          <view v-for="(item, index) in paginatedTableData" :key="index" class="tr">
            <view class="td">{{ item.statisticsTime }}</view>
            <view class="td">{{ item.shareUsage }}</view>
            <view class="td">{{ item.usage }}</view>
<!--            <view class="td" :class="{'up': Number(item.onYear) > 0, 'down': Number(item.onYear) < 0}">-->
<!--              {{ item.onYear }}%-->
<!--            </view>-->
<!--            <view class="td" :class="{'up': Number(item.chainRate) > 0, 'down': Number(item.chainRate) < 0}">-->
<!--              {{ item.chainRate }}%-->
<!--            </view>-->
            <view class="td">¥{{ item.unitAmt }}</view>
            <view class="td">¥{{ item.allAmt }}</view>
          </view>
        </scroll-view>
      </view>
      <view class="pagination">
        <uni-pagination
            :total="total"
            :pageSize="pageSize"
            :current="currentPage"
            @change="handleCurrentChange"
        />
      </view>
    </view>
  </view>
</template>

<script>
import { qryStatisticsByType } from '@/api/HRP/weg';
import { getPerSquareEnergyPeakValue } from '@/api/portal/statistics';
import DayPicker from '@/components/MyFormComponents/time-picker/DayPicker.vue';
import MonthPicker from '@/components/MyFormComponents/time-picker/MonthPicker.vue';
import YearPicker from '@/components/MyFormComponents/time-picker/YearPicker.vue';

export default {
  name: "viewHospPerPeakEnergy",
  components: {
    DayPicker,
    MonthPicker,
    YearPicker
  },
  data() {
    return {
      // 能耗类型选项
      energyTypeOptions: [
        { value: 1, text: '用电' },
        { value: 2, text: '用水' },
        { value: 3, text: '用气' }
      ],
      // 时间周期选项
      timePeriodOptions: [
        { value: 1, text: '日' },
        { value: 2, text: '月' },
        { value: 3, text: '年' }
      ],
      timePeriod: 1,  // 默认为日周期
      energyType: 1,  // 默认为用电类型
      deptId: -1,     // 部门ID，-1代表全院
      dateRange: [],  // 日期范围数组，用于API请求参数

      // 自定义时间选择器数据
      dateRangeArray: [],    // 日期范围数组，格式：['2024-01-01', '2024-01-31']
      monthRangeArray: [],   // 月份范围数组，格式：['2024-01', '2024-03']
      yearRangeArray: [],    // 年份范围数组，格式：['2023', '2024']

      // 保留原有数据属性以兼容现有逻辑
      startDate: '',
      endDate: '',
      startDateModel: '',
      endDateModel: '',
      startMonth: '',
      endMonth: '',
      startMonthModel: '',
      endMonthModel: '',
      startYear: '',
      endYear: '',
      startYearModel: '',
      endYearModel: '',

      // 图表数据结构
      chartData: {
        categories: [], // X轴分类
        series: []      // 数据系列
      },
      tableData: [],    // 表格数据源
      currentPage: 1,   // 分页当前页
      pageSize: 10,     // 每页记录数
      total: 0,         // 总记录数

      // 峰值数据结构，存储不同能源类型和时间周期的峰值标准
      peakValue: {
        dayPerSquareElectricityPeakValue: null, // 日用电峰值
        dayPerSquareWaterPeakValue: null,       // 日用水峰值
        dayPerSquareGasPeakValue: null,         // 日用气峰值
        monthPerSquareElectricityPeakValue: null, // 月用电峰值
        monthPerSquareWaterPeakValue: null,       // 月用水峰值
        monthPerSquareGasPeakValue: null,         // 月用气峰值
        yearPerSquareElectricityPeakValue: null,  // 年用电峰值
        yearPerSquareWaterPeakValue: null,        // 年用水峰值
        yearPerSquareGasPeakValue: null,          // 年用气峰值
        effectivePeak: null                        // 当前有效峰值
      },

      // 图表配置选项
      chartOpts: {
        animation: true,
        color: ['#409EFF', '#67C23A', '#E6A23C', '#FFA500'], // 颜色配置: 蓝色柱图, 绿色同比, 橙色环比, 橙色峰值线
        padding: [15, 75, 15, 15], // 上右下左四边内边距，右侧加大为峰值标签预留空间
        legend: {
          show: true,
          position: 'bottom',
          float: 'center',
          padding: 10,
          margin: 5,
          fontSize: 13,
          fontColor: '#666666',
          lineHeight: 20,
          itemGap: 15,
          borderWidth: 1,
          borderColor: '#f0f0f0',
          backgroundColor: 'rgba(255,255,255,0.9)',
          borderRadius: 4,
          formatter: (name) => {
            // 为峰值线添加虚线样式的图例
            if (name.includes('峰值')) {
              return {
                text: name,
                lineDash: [5, 5] // 显示为虚线样式的图例
              };
            }
            // 添加超出峰值的红色提示图例
            if (name === '均摊用量') {
              return [
                { text: name, color: '#409EFF' },
                { text: '超出峰值', color: '#ff4d4f' }
              ];
            }
            return name;
          }
        },
        xAxis: {
          disableGrid: true,
          boundaryGap: true,
          axisLine: true,
          axisLineColor: '#CCCCCC',
          labelRotate: 60, // 增加旋转角度到60度
          fontSize: 10,    // 小字体
          margin: 20,      // 增大边距到20
          itemCount: 5,    // 一次只显示5个标签，减少密度
          scrollShow: true, // 显示滚动条
          scrollAlign: 'left', // 滚动对齐方式
          scrollBackgroundColor: '#F5F5F5', // 滚动条背景色
          scrollColor: '#409EFF', // 滚动条颜色
          format: (val) => {
            // 简化日期格式
            if (this.timePeriod === 1) { // 日
              const parts = val.split('-');
              if (parts.length === 3) {
                return `${parts[1]}-${parts[2]}`; // 只显示月-日
              }
            } else if (this.timePeriod === 2) { // 月
              const parts = val.split('-');
              if (parts.length === 2) {
                return parts[1]; // 只显示月
              }
            }
            return val;
          }
        },
        yAxis: [
          {
            data: {
              min: 0,
              // 防止Y轴显示null
              formatter: (val) => {
                if (val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                  return '';
                }
                return val;
              }
            },
            title: '均摊用量',
            titleFontSize: 12,
            disabled: false,
            fontColor: '#666666',
            gridColor: '#CCCCCC',
            gridType: 'dash',
            dashLength: 4,
            axisLine: true,
            axisLineColor: '#CCCCCC',
            // 防止Y轴标签显示null
            formatter: (val) => {
              if (val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                return '';
              }
              return val;
            },
            // 完全过滤null值
            labelFormat: (val) => {
              if (!val || val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                return '';
              }
              return val;
            }
          },
          {
            data: {
              min: -100,
              max: 100,
              // 防止Y轴显示null
              formatter: (val) => {
                if (val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                  return '';
                }
                return val;
              }
            },
            title: '增长率(%)',
            titleFontSize: 12,
            right: true,
            disabled: false,
            fontColor: '#666666',
            gridColor: '#CCCCCC',
            gridType: 'dash',
            dashLength: 4,
            axisLine: true,
            axisLineColor: '#CCCCCC',
            // 防止Y轴标签显示null
            formatter: (val) => {
              if (val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                return '';
              }
              return val;
            },
            // 完全过滤null值
            labelFormat: (val) => {
              if (!val || val === null || val === undefined || String(val).toLowerCase().includes('null')) {
                return '';
              }
              return val;
            }
          }
        ],
        extra: {
          column: {
            width: 30,
            barBorderRadius: [6, 6, 0, 0]
          },
          line: {
            connectNulls: true, // 确保线条连接空值点
            width: 2.5
          },
          tooltip: {
            showBox: true,
            showArrow: true,
            showCategory: true,
            borderWidth: 0,
            borderRadius: 4,
            borderColor: '#000000',
            borderOpacity: 0.7,
            bgColor: '#000000',
            bgOpacity: 0.7,
            gridType: 'solid',
            dashLength: 4,
            gridColor: '#CCCCCC',
            fontColor: '#FFFFFF',
            splitLine: true,
            horizentalLine: true,
            xAxisLabel: true,
            yAxisLabel: true,
            labelBgColor: '#FFFFFF',
            labelBgOpacity: 0.7,
            labelFontColor: '#666666'
          }
        },
        title: {
          name: '',
          fontSize: 16,
          color: '#333',
          offsetY: 5
        },
        type: 'mix',              // 使用混合图表类型
        dataLabel: false,         // 关闭所有数据标签
        dataPointShape: false,    // 关闭所有数据点
        enableScroll: true,       // 启用滚动
        touchMoveLimit: 60,       // 添加触摸移动限制，提高灵敏度
        enableMarkLine: true,     // 启用标记线方法
        yAxisWidth: 60,           // Y轴宽度
        background: '#fff',       // 背景颜色
        enableLegend: true,       // 启用图例
      },
      tableHeight: 400,    // 表格高度
      chartHeight: 800,    // 图表高度
      chartInstance: null, // 图表实例引用
      yAxisMax: 0,         // Y轴最大值
      windowWidth: 0,      // 添加窗口宽度属性
      windowHeight: 0,     // 添加窗口高度属性
      themeColors: {
        primary: '#409EFF',    // 主色
        success: '#67C23A',    // 成功色
        warning: '#E6A23C',    // 警告色
        danger: '#F56C6C',     // 危险色
        peak: '#FFA500',       // 峰值色
        up: '#F56C6C',         // 上升色
        down: '#67C23A',       // 下降色
        background: '#F5F7FA'   // 背景色
      }
    }
  },

  computed: {
    // 根据能耗类型获取对应文本标签
    energyTypeLabel() {
      const option = this.energyTypeOptions.find(item => item.value === this.energyType);
      return option ? option.text : '';
    },

    // 获取当前页的表格数据
    paginatedTableData() {
      const start = (this.currentPage - 1) * this.pageSize;
      const end = start + this.pageSize;
      return this.tableData.slice(start, end);
    },

    // 转换时间周期到API所需的timeType参数
    timeType() {
      // 1:日->2, 2:月->3, 3:年->4
      return this.timePeriod + 1;
    }
  },

  /**
   * 页面加载时的初始化操作
   */
  onLoad() {
    // 设置默认时间范围并加载数据
    this.setQuickRange();
    this.testPeakValueAPI();

    // 获取并存储窗口尺寸
    this.getSystemInfo();

    // 移除对window的直接引用，使用uni-app的API代替
    // window.addEventListener('resize', this.handleResize);

    // 应用主题颜色
    this.applyThemeColors();
  },

  /**
   * 页面卸载时清理事件监听器
   */
  onUnload() {
    // 移除对window的直接引用
    // window.removeEventListener('resize', this.handleResize);
  },

  /**
   * 页面显示时的处理函数
   */
  onShow() {
    // 页面显示时重新获取系统信息，替代resize事件
    this.getSystemInfo();
  },

  /**
   * 页面尺寸变化时的处理函数
   */
  onResize() {
    // 页面尺寸变化时重新获取系统信息
    this.getSystemInfo();
  },

  methods: {
    /**
     * 获取系统信息并调整UI尺寸
     * 根据当前设备窗口大小调整表格和图表的尺寸
     */
    getSystemInfo() {
      try {
        const systemInfo = uni.getSystemInfoSync();
        this.windowWidth = systemInfo.windowWidth;
        this.windowHeight = systemInfo.windowHeight;

        console.log('当前窗口尺寸:', this.windowWidth, 'x', this.windowHeight);

        // 根据窗口大小调整表格和图表高度
        this.adjustComponentSizes();
      } catch (e) {
        console.error('获取系统信息失败:', e);
      }
    },

    /**
     * 处理窗口大小变化事件
     */
    handleResize() {
      console.log('窗口大小已变化，重新调整组件尺寸');
      this.getSystemInfo();
    },

    /**
     * 根据窗口大小调整组件尺寸
     * 自适应调整图表和表格的尺寸，确保在不同设备上有最佳显示效果
     */
    adjustComponentSizes() {
      // 计算图表高度 - 根据窗口高度动态调整
      const isSmallScreen = this.windowHeight < 700;
      const isMediumScreen = this.windowHeight >= 700 && this.windowHeight < 900;

      // 根据屏幕大小设置不同的图表高度
      if (isSmallScreen) {
        this.chartHeight = 600; // 小屏幕使用较小的图表
      } else if (isMediumScreen) {
        this.chartHeight = 700; // 中等屏幕
      } else {
        this.chartHeight = 800; // 大屏幕使用完整大小
      }

      // 计算表格高度 - 大约为窗口高度的40%
      this.tableHeight = Math.max(300, Math.round(this.windowHeight * 0.4));

      console.log('调整后的组件尺寸:', {
        图表高度: this.chartHeight,
        表格高度: this.tableHeight
      });

      // 如果图表已初始化，通知图表重新渲染以适应新尺寸
      if (this.chartInstance) {
        this.$nextTick(() => {
          // 检查图表实例是否有resize方法
          if (typeof this.chartInstance.resize === 'function') {
            this.chartInstance.resize();
            console.log('图表尺寸已调整');
          } else if (typeof this.chartInstance.refresh === 'function') {
            this.chartInstance.refresh();
            console.log('图表已刷新');
          } else {
            console.log('图表实例没有resize或refresh方法，跳过尺寸调整');
          }
        });
      }
    },

    /**
     * 图表初始化完成时的回调
     * @param {Object} chart - 图表实例
     */
    onChartInit(chart) {
      console.log('图表初始化完成', chart);
      this.chartInstance = chart;

      // 检查图表实例的可用方法
      if (this.chartInstance) {
        console.log('图表实例方法:', Object.getOwnPropertyNames(this.chartInstance));
        console.log('图表实例原型方法:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.chartInstance)));

        // 对于qiun-data-charts，通常不需要手动调用updateData
        // 图表会根据chartData的变化自动更新
        console.log('图表初始化完成，等待数据更新');
      }
    },

    /**
     * 图表错误处理
     * @param {Object} error - 错误信息
     */
    onChartError(error) {
      console.error('图表渲染错误:', error);
      uni.showToast({
        title: '图表加载失败',
        icon: 'none',
        duration: 2000
      });
    },

    /**
     * 图表项点击事件
     * @param {Object} event - 点击事件信息
     */
    onItemTap(event) {
      console.log('图表项被点击:', event);
      // 可以在这里添加点击图表项的交互逻辑
    },

    /**
     * 处理年份模式的峰值线
     * 年份模式下的特殊峰值线处理逻辑
     */
    handleYearModePeakLine() {
      console.log('处理年份模式峰值线');
      // 确保有年份峰值数据
      this.ensurePeakValues();

      // 如果有图表数据，重新更新图表以应用年份模式的峰值线
      if (this.chartData.categories && this.chartData.categories.length > 0) {
        this.updateChartData();
      }
    },

    /**
     * 确保有峰值数据，没有时设置默认值
     * 根据当前的时间周期和能耗类型检查并设置相应的峰值
     */
    ensurePeakValues() {
      try {
        console.log('确保峰值数据存在');

        // 根据时间类型和能耗类型检查峰值数据
        let peak = 0;
        if (this.timePeriod === 1) { // 日
          if (this.energyType === 1) peak = this.peakValue.dayPerSquareElectricityPeakValue;
          else if (this.energyType === 2) peak = this.peakValue.dayPerSquareWaterPeakValue;
          else if (this.energyType === 3) peak = this.peakValue.dayPerSquareGasPeakValue;
        } else if (this.timePeriod === 2) { // 月
          if (this.energyType === 1) peak = this.peakValue.monthPerSquareElectricityPeakValue;
          else if (this.energyType === 2) peak = this.peakValue.monthPerSquareWaterPeakValue;
          else if (this.energyType === 3) peak = this.peakValue.monthPerSquareGasPeakValue;
        } else if (this.timePeriod === 3) { // 年
          if (this.energyType === 1) peak = this.peakValue.yearPerSquareElectricityPeakValue;
          else if (this.energyType === 2) peak = this.peakValue.yearPerSquareWaterPeakValue;
          else if (this.energyType === 3) peak = this.peakValue.yearPerSquareGasPeakValue;
        }

        console.log('当前峰值数据:', peak);

        // 如果没有有效峰值，使用默认值
        if (!peak || peak <= 0) {
          console.log('使用默认峰值数据');

          // 设置默认峰值 - 根据时间类型设置不同的默认值
          if (this.timePeriod === 1) { // 日
            peak = 100; // 日峰值默认值
          } else if (this.timePeriod === 2) { // 月
            peak = 3000; // 月峰值默认值
          } else if (this.timePeriod === 3) { // 年
            peak = 36000; // 年峰值默认值
          }

          // 更新相应的峰值字段
          if (this.timePeriod === 1) { // 日
            if (this.energyType === 1) this.peakValue.dayPerSquareElectricityPeakValue = peak;
            else if (this.energyType === 2) this.peakValue.dayPerSquareWaterPeakValue = peak;
            else if (this.energyType === 3) this.peakValue.dayPerSquareGasPeakValue = peak;
          } else if (this.timePeriod === 2) { // 月
            if (this.energyType === 1) this.peakValue.monthPerSquareElectricityPeakValue = peak;
            else if (this.energyType === 2) this.peakValue.monthPerSquareWaterPeakValue = peak;
            else if (this.energyType === 3) this.peakValue.monthPerSquareGasPeakValue = peak;
          } else if (this.timePeriod === 3) { // 年
            if (this.energyType === 1) this.peakValue.yearPerSquareElectricityPeakValue = peak;
            else if (this.energyType === 2) this.peakValue.yearPerSquareWaterPeakValue = peak;
            else if (this.energyType === 3) this.peakValue.yearPerSquareGasPeakValue = peak;
          }

          console.log('更新后的峰值数据:', this.peakValue);
        }
      } catch (error) {
        console.error('确保峰值数据时出错:', error);
      }
    },

    /**
     * 测试峰值API是否正常工作
     * 获取峰值数据，确保图表能够显示峰值线
     */
    testPeakValueAPI() {
      console.log('测试峰值API...');
      getPerSquareEnergyPeakValue()
          .then(res => {
            console.log('峰值API测试结果:', res);
            if (res && res.code === 200 && res.data) {
              this.peakValue = res.data;
              console.log('预加载峰值数据:', this.peakValue);

              // 添加测试代码 - 确保有峰值数据
              this.ensurePeakValues();

              // 立即更新图表以测试峰值线
              if (this.chartData.categories && this.chartData.categories.length > 0) {
                this.updateChartData();
              }
            } else {
              // 如果API返回失败，使用测试数据
              this.ensurePeakValues();
            }
          })
          .catch(err => {
            console.error('峰值API测试失败:', err);
            // 发生错误时使用测试数据
            this.ensurePeakValues();
          });
    },

    /**
     * 能耗类型变更处理函数
     * @param {Number} value - 选中的能耗类型值
     */
    onEnergyTypeChange(value) {
      this.energyType = value;
      this.fetchData();
    },

    /**
     * 时间周期变更处理函数
     * @param {Number} value - 选中的时间周期值
     */
    onTimePeriodChange(value) {
      this.timePeriod = value;
      console.log('切换时间周期为:', value, this.timePeriodOptions.find(item => item.value === value)?.text);
      this.setQuickRange();
      // 在切换时间类型后，确保有峰值数据
      this.ensurePeakValues();

      // 如果切换到年份模式，额外调用年份模式峰值线处理
      if (value === 3) {
        // 在数据加载完成后处理峰值线
        setTimeout(() => {
          this.handleYearModePeakLine();
        }, 500);
      }
    },

    /**
     * 设置快速时间范围
     * 根据当前选择的时间周期，设置默认的时间范围
     */
    setQuickRange() {
      const now = new Date();
      if (this.timePeriod === 1) { // 日
        const end = new Date();
        const start = new Date();
        start.setDate(end.getDate() - 6);

        const startDateStr = start.toISOString().split('T')[0];
        const endDateStr = end.toISOString().split('T')[0];

        this.startDate = startDateStr;
        this.endDate = endDateStr;
        this.startDateModel = startDateStr;
        this.endDateModel = endDateStr;

        // 同时更新自定义组件的值
        this.dateRangeArray = [startDateStr, endDateStr];

        this.dateRange = [startDateStr, endDateStr];
      } else if (this.timePeriod === 2) { // 月
        const year = now.getFullYear();
        const month = String(now.getMonth() + 1).padStart(2, '0');
        const monthStr = `${year}-${month}`;

        this.startMonth = monthStr;
        this.endMonth = monthStr;
        this.startMonthModel = monthStr;
        this.endMonthModel = monthStr;

        // 同时更新自定义组件的值
        this.monthRangeArray = [monthStr, monthStr];

        const lastDay = new Date(year, parseInt(month), 0).getDate();
        this.dateRange = [
          `${year}-${month}-01`,
          `${year}-${month}-${lastDay}`
        ];
      } else if (this.timePeriod === 3) { // 年
        const year = now.getFullYear();
        const yearStr = String(year);

        this.startYear = yearStr;
        this.endYear = yearStr;
        this.startYearModel = yearStr;
        this.endYearModel = yearStr;

        // 同时更新自定义组件的值
        this.yearRangeArray = [yearStr, yearStr];

        this.dateRange = [
          `${yearStr}-01-01`,
          `${yearStr}-12-31`
        ];
      }

      this.fetchData();
    },

    // 自定义日期选择器处理方法
    handleDateRangeChange(dateRange) {
      console.log('日期范围变化:', dateRange);
      this.dateRangeArray = dateRange || [];

      if (dateRange && dateRange.length === 2 && dateRange[0] && dateRange[1]) {
        this.startDate = dateRange[0];
        this.endDate = dateRange[1];
        this.startDateModel = dateRange[0];
        this.endDateModel = dateRange[1];
        this.dateRange = [dateRange[0], dateRange[1]];
        this.fetchData();
      }
    },

    // 自定义月份选择器处理方法
    handleMonthRangeChange(monthRange) {
      console.log('月份范围变化:', monthRange);
      this.monthRangeArray = monthRange || [];

      if (monthRange && monthRange.length === 2 && monthRange[0] && monthRange[1]) {
        this.startMonth = monthRange[0];
        this.endMonth = monthRange[1];
        this.startMonthModel = monthRange[0];
        this.endMonthModel = monthRange[1];

        const [startYear, startMonth] = monthRange[0].split('-');
        const [endYear, endMonth] = monthRange[1].split('-');
        this.dateRange = [
          `${startYear}-${startMonth}-01`,
          this.getLastDayOfMonth(endYear, endMonth)
        ];
        this.fetchData();
      }
    },

    // 自定义年份选择器处理方法
    handleYearRangeChange(yearRange) {
      console.log('年份范围变化:', yearRange);
      this.yearRangeArray = yearRange || [];

      if (yearRange && yearRange.length === 2 && yearRange[0] && yearRange[1]) {
        this.startYear = yearRange[0];
        this.endYear = yearRange[1];
        this.startYearModel = yearRange[0];
        this.endYearModel = yearRange[1];

        this.dateRange = [
          `${yearRange[0]}-01-01`,
          `${yearRange[1]}-12-31`
        ];
        this.fetchData();
      }
    },

    // 日期处理方法保持不变
    handleStartDateChange(value) {
      this.startDate = value;
      this.startDateModel = value;
      if (this.endDate) {
        this.dateRange = [this.startDate, this.endDate];
        this.fetchData();
      }
    },
    handleEndDateChange(value) {
      this.endDate = value;
      this.endDateModel = value;
      if (this.startDate) {
        this.dateRange = [this.startDate, this.endDate];
        this.fetchData();
      }
    },
    // 月份处理方法保持不变
    handleStartMonthChange(value) {
      this.startMonth = value;
      this.startMonthModel = value;
      if (this.endMonth) {
        const [startYear, startMonth] = this.startMonth.split('-');
        const [endYear, endMonth] = this.endMonth.split('-');
        this.dateRange = [
          `${startYear}-${startMonth}-01`,
          this.getLastDayOfMonth(endYear, endMonth)
        ];
        this.fetchData();
      }
    },
    handleEndMonthChange(value) {
      this.endMonth = value;
      this.endMonthModel = value;
      if (this.startMonth) {
        const [startYear, startMonth] = this.startMonth.split('-');
        const [endYear, endMonth] = this.endMonth.split('-');
        this.dateRange = [
          `${startYear}-${startMonth}-01`,
          this.getLastDayOfMonth(endYear, endMonth)
        ];
        this.fetchData();
      }
    },
    // 年份处理方法保持不变
    handleStartYearChange(value) {
      this.startYear = value;
      this.startYearModel = value;
      if (this.endYear) {
        this.dateRange = [
          `${this.startYear}-01-01`,
          `${this.endYear}-12-31`
        ];
        this.fetchData();
      }
    },
    handleEndYearChange(value) {
      this.endYear = value;
      this.endYearModel = value;
      if (this.startYear) {
        this.dateRange = [
          `${this.startYear}-01-01`,
          `${this.endYear}-12-31`
        ];
        this.fetchData();
      }
    },

    getLastDayOfMonth(year, month) {
      const lastDay = new Date(year, month, 0).getDate();
      return `${year}-${month}-${lastDay}`;
    },

    // 实现参考代码中的快速日期选择方法
    setLastSevenDays() {
      const end = new Date();
      const start = new Date();
      start.setDate(end.getDate() - 6);

      const startDateStr = start.toISOString().split('T')[0];
      const endDateStr = end.toISOString().split('T')[0];

      this.startDate = startDateStr;
      this.endDate = endDateStr;
      this.startDateModel = startDateStr;
      this.endDateModel = endDateStr;

      // 同时更新自定义组件的值
      this.dateRangeArray = [startDateStr, endDateStr];

      this.dateRange = [startDateStr, endDateStr];
      this.fetchData();
    },

    setCurrentMonth() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const startDate = `${year}-${month}-01`;
      const endDate = now.toISOString().split('T')[0];

      this.startDate = startDate;
      this.endDate = endDate;
      this.startDateModel = startDate;
      this.endDateModel = endDate;

      // 同时更新自定义组件的值
      this.dateRangeArray = [startDate, endDate];

      this.dateRange = [startDate, endDate];
      this.fetchData();
    },

    setCurrentMonthOnly() {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, '0');
      const monthStr = `${year}-${month}`;

      this.startMonth = monthStr;
      this.endMonth = monthStr;
      this.startMonthModel = monthStr;
      this.endMonthModel = monthStr;

      // 同时更新自定义组件的值
      this.monthRangeArray = [monthStr, monthStr];

      const lastDay = new Date(year, parseInt(month), 0).getDate();
      this.dateRange = [
        `${year}-${month}-01`,
        `${year}-${month}-${lastDay}`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    setLastThreeMonths() {
      const now = new Date();
      const endYear = now.getFullYear();
      const endMonth = String(now.getMonth() + 1).padStart(2, '0');
      const start = new Date();
      start.setMonth(now.getMonth() - 2);
      const startYear = start.getFullYear();
      const startMonth = String(start.getMonth() + 1).padStart(2, '0');

      const startMonthStr = `${startYear}-${startMonth}`;
      const endMonthStr = `${endYear}-${endMonth}`;

      this.startMonth = startMonthStr;
      this.endMonth = endMonthStr;
      this.startMonthModel = startMonthStr;
      this.endMonthModel = endMonthStr;

      // 同时更新自定义组件的值
      this.monthRangeArray = [startMonthStr, endMonthStr];

      const lastDay = new Date(endYear, parseInt(endMonth), 0).getDate();
      this.dateRange = [
        `${startYear}-${startMonth}-01`,
        `${endYear}-${endMonth}-${lastDay}`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    setLastSixMonths() {
      const now = new Date();
      const endYear = now.getFullYear();
      const endMonth = String(now.getMonth() + 1).padStart(2, '0');
      const start = new Date();
      start.setMonth(now.getMonth() - 5);
      const startYear = start.getFullYear();
      const startMonth = String(start.getMonth() + 1).padStart(2, '0');

      const startMonthStr = `${startYear}-${startMonth}`;
      const endMonthStr = `${endYear}-${endMonth}`;

      this.startMonth = startMonthStr;
      this.endMonth = endMonthStr;
      this.startMonthModel = startMonthStr;
      this.endMonthModel = endMonthStr;

      // 同时更新自定义组件的值
      this.monthRangeArray = [startMonthStr, endMonthStr];

      const lastDay = new Date(endYear, parseInt(endMonth), 0).getDate();
      this.dateRange = [
        `${startYear}-${startMonth}-01`,
        `${endYear}-${endMonth}-${lastDay}`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    setCurrentYear() {
      const year = new Date().getFullYear();
      const yearStr = String(year);

      this.startYear = yearStr;
      this.endYear = yearStr;
      this.startYearModel = yearStr;
      this.endYearModel = yearStr;

      // 同时更新自定义组件的值
      this.yearRangeArray = [yearStr, yearStr];

      this.dateRange = [
        `${yearStr}-01-01`,
        `${yearStr}-12-31`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    setLastYear() {
      const now = new Date();
      const lastYear = now.getFullYear() - 1;
      const thisYear = now.getFullYear();

      const lastYearStr = String(lastYear);
      const thisYearStr = String(thisYear);

      this.startYear = lastYearStr;
      this.endYear = thisYearStr;
      this.startYearModel = lastYearStr;
      this.endYearModel = thisYearStr;

      // 同时更新自定义组件的值
      this.yearRangeArray = [lastYearStr, thisYearStr];

      this.dateRange = [
        `${lastYear}-01-01`,
        `${thisYear}-12-31`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    setLastThreeYears() {
      const now = new Date();
      const startYear = now.getFullYear() - 2;
      const endYear = now.getFullYear();

      const startYearStr = String(startYear);
      const endYearStr = String(endYear);

      this.startYear = startYearStr;
      this.endYear = endYearStr;
      this.startYearModel = startYearStr;
      this.endYearModel = endYearStr;

      // 同时更新自定义组件的值
      this.yearRangeArray = [startYearStr, endYearStr];

      this.dateRange = [
        `${startYear}-01-01`,
        `${endYear}-12-31`
      ];

      this.$nextTick(() => {
        this.fetchData();
      });
    },

    handleCurrentChange(e) {
      this.currentPage = e.current;
    },

    // 修改为参考代码的数据获取方式
    async fetchData() {
      console.log('开始获取数据...');
      if (!this.dateRange || this.dateRange.length !== 2) return;

      // 组装参数
      let startTime = this.dateRange[0];
      let endTime = this.dateRange[1];

      // 月、年需要补全格式（如果还没有）
      if (this.timePeriod === 2 && !startTime.includes('-01')) {
        startTime = `${startTime}-01`;
        endTime = `${endTime}-01`;
      } else if (this.timePeriod === 3 && !startTime.includes('-01-01')) {
        startTime = `${startTime}-01-01`;
        endTime = `${endTime}-12-31`;
      }

      const params = {
        timeType: this.timeType,
        startTime,
        endTime,
        typeId: null,
        orgId: this.deptId,
        qryType: this.energyType
      };

      console.log('请求参数:', params);

      try {
        console.log('请求能耗数据和峰值数据...');

        // 单独请求能耗数据
        const res = await qryStatisticsByType(params);
        console.log('能耗统计数据返回结果:', res);

        // 单独请求峰值数据
        try {
          console.log('请求峰值数据...');
          const peakRes = await getPerSquareEnergyPeakValue();
          console.log('峰值数据返回结果:', peakRes);

          if (peakRes && peakRes.code === 200 && peakRes.data) {
            this.peakValue = peakRes.data;
            console.log('更新后的峰值数据:', this.peakValue);
          } else {
            console.warn('峰值数据格式不正确或为空:', peakRes);
          }
        } catch (peakError) {
          console.error('获取峰值数据失败:', peakError);
        }

        // 处理能耗数据
        if (res.code === 200) {
          const dayMap = new Map();
          // 确保res.data是数组
          const dataArray = Array.isArray(res.data) ? res.data : (res.data ? [res.data] : []);

          dataArray.forEach(item => {
            if (!item || !item.calcTime) return;

            let statisticsTime = '';
            if (this.timePeriod === 1) { // 日
              const calcTime = String(item.calcTime);
              const year = calcTime.substring(0, 4);
              const month = calcTime.substring(4, 6);
              const day = calcTime.substring(6, 8);
              statisticsTime = `${year}-${month}-${day}`;
            } else if (this.timePeriod === 2) { // 月
              const calcTime = String(item.calcTime);
              const year = calcTime.substring(0, 4);
              const month = calcTime.substring(4, 6);
              statisticsTime = `${year}-${month}`;
            } else if (this.timePeriod === 3) { // 年
              statisticsTime = String(item.calcTime).substring(0, 4);
            }

            if (statisticsTime && !dayMap.has(statisticsTime)) {
              dayMap.set(statisticsTime, {
                statisticsTime,
                shareUsage: item.shareUsage !== null ? Number(item.shareUsage).toFixed(4) : '-',
                usage: item.usage !== null ? Number(item.usage).toFixed(4) : '-',
                onYear: item.onYear !== null ? (Number(item.onYear) * 100).toFixed(2) : '-',
                chainRate: item.chainRate !== null ? (Number(item.chainRate) * 100).toFixed(2) : '-',
                area: item.area !== null ? Number(item.area).toFixed(4) : '-',
                unitAmt: item.unitAmt !== null ? Number(item.unitAmt).toFixed(2) : '-',
                allAmt: item.allAmt !== null ? Number(item.allAmt).toFixed(2) : '-'
              });
            }
          });

          this.tableData = Array.from(dayMap.values()).sort((a, b) => {
            return new Date(a.statisticsTime) - new Date(b.statisticsTime);
          });

          this.total = this.tableData.length;
          this.currentPage = 1;

          console.log('处理后的表格数据:', this.tableData);

          // 更新图表数据
          this.$nextTick(() => {
            this.updateChartData();
            // 增强图表滚动功能
            this.enhanceChartScroll();
          });
        } else {
          console.warn('获取能耗数据失败:', res);
          this.tableData = [];
          this.total = 0;
          this.currentPage = 1;
          this.chartData = {
            categories: [],
            series: []
          };
        }
      } catch (error) {
        console.error('获取数据失败:', error);
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
        this.tableData = [];
        this.total = 0;
        this.currentPage = 1;
        this.chartData = {
          categories: [],
          series: []
        };
      }
    },

    /**
     * 增强图表滚动功能
     * 针对图表滚动问题进行特殊处理
     */
    enhanceChartScroll() {
      // 获取数据点数量
      const categoryCount = this.chartData.categories?.length || 0;

      // 数据点太少不需要特别处理
      if (categoryCount <= 10) return;

      console.log(`增强图表滚动功能，共有${categoryCount}个数据点`);

      // 添加水平滚动相关配置
      this.chartOpts.enableScroll = true; // 强制启用滚动
      this.chartOpts.touchMoveLimit = 30; // 增加触摸检测灵敏度，设置更小的值提高灵敏度
      this.chartOpts.ontap = true; // 启用点击事件
      this.chartOpts.onmovetip = true; // 启用移动提示
      this.chartOpts.onzoom = false; // 禁用缩放功能，避免与滑动冲突

      // 1. 设置初始视图范围 - 显示最近的数据
      // 根据数据量确定初始显示的数据点数
      let visiblePoints = 0;
      if (categoryCount > 60) {
        visiblePoints = 10; // 数据量极大时只显示最近10个点
      } else if (categoryCount > 30) {
        visiblePoints = 15; // 数据量较大时显示最近15个点
      } else if (categoryCount > 20) {
        visiblePoints = 10; // 数据量中等时显示最近10个点
      } else {
        visiblePoints = 8; // 默认显示最近8个点
      }

      // 2. 设置滚动区域配置
      this.chartOpts.extra = this.chartOpts.extra || {};
      this.chartOpts.extra.scrollArea = {
        width: Math.min(750, this.windowWidth * 2), // 滚动区域宽度
        height: 'auto',
        offsetLeft: 0,
        offsetTop: 0
      };

      // 3. 设置滚动条样式
      this.chartOpts.extra.scrollBar = {
        show: true, // 显示滚动条
        width: '80%', // 滚动条宽度
        height: 6, // 滚动条高度
        color: '#DCE0E6', // 滚动条颜色
        backgroundColor: '#F5F5F5', // 滚动条背景色
        borderRadius: 10, // 滚动条圆角
        marginBottom: 5 // 滚动条与图表底部距离
      };

      // 4. 计算每个数据点的宽度和初始滚动位置
      const totalWidth = this.chartOpts.extra.scrollArea.width;
      const pointWidth = totalWidth / categoryCount;

      // 5. 设置滚动配置
      this.chartOpts.extra.scroll = {
        direction: 'horizontal', // 水平滚动
        defaultScrollLeft: totalWidth - (visiblePoints * pointWidth), // 默认滚动到右侧(最新数据)
        scrollThreshold: 10, // 滚动阈值
        touchScroll: true, // 允许触摸滚动
        itemCount: categoryCount, // 数据项总数
        visibleItemCount: visiblePoints // 可见数据项数量
      };

      // 6. 对于qiun-data-charts，滚动配置通过chartOpts传递，不需要手动调用updateData
      console.log('滚动配置已更新到chartOpts，图表将在下次数据更新时应用新配置');

      console.log(`滚动优化完成: 显示${visiblePoints}/${categoryCount}个数据点`);
    },

    optimizeChartLabels() {
      // 获取数据点数量
      const categoryCount = this.chartData.categories?.length || 0;

      // 无数据时直接返回
      if (categoryCount === 0) return;

      console.log(`检测到${categoryCount}个数据点，开始优化标签显示`);

      // 1. 自适应调整标签旋转角度
      if (categoryCount > 20) {
        this.chartOpts.xAxis.labelRotate = 60; // 数据多于20个点时，增加旋转角度
      } else if (categoryCount > 10) {
        this.chartOpts.xAxis.labelRotate = 45; // 中等数据量
      } else {
        this.chartOpts.xAxis.labelRotate = 30; // 数据很少时，缩小旋转角度
      }

      // 2. 自适应调整柱状图宽
      if (categoryCount > 30) {
        // 数据点多时，缩小柱状图宽度
        this.chartOpts.extra.column.width = 15;
      } else if (categoryCount > 20) {
        this.chartOpts.extra.column.width = 20;
      } else {
        this.chartOpts.extra.column.width = 30; // 默认宽
      }

      // 3. 根据时间范围自动开启滚动
      this.chartOpts.enableScroll = categoryCount > 10;

      // 4. 自适应调整X轴字体大小
      if (categoryCount > 30) {
        this.chartOpts.xAxis.fontSize = 9; // 更小字体大小
      } else if (categoryCount > 20) {
        this.chartOpts.xAxis.fontSize = 10;
      } else {
        this.chartOpts.xAxis.fontSize = 11; // 不缩小字体
      }

      console.log(`优化完成：旋转角度=${this.chartOpts.xAxis.labelRotate}度，开启滚动=${this.chartOpts.enableScroll}`);

      // 5. 百分比的标签缩小
      if (this.chartOpts.yAxis && this.chartOpts.yAxis.length > 1) {
        this.chartOpts.yAxis[1].titleFontSize = 10; // 增长率标题缩小
        this.chartOpts.yAxis[1].formatter = (val) => {
          if (val === null || val === undefined || String(val).toLowerCase().includes('null')) {
            return '';
          }
          // 当数据点多时，可以只显示整数
          return categoryCount > 20 ? parseInt(val) : val;
        };
      }
    },

    /**
     * 更新图表数据
     * @override 修复滑动功能问题
     */
    updateChartData() {
      console.log('开始更新图表数据，修复滑动功能');

      if (!this.tableData || this.tableData.length === 0) {
        console.warn('表格数据为空，无法更新图表');
        this.chartData = {
          categories: [],
          series: []
        };
        return;
      }

      // 生成完整日期范围
      const completeDateRange = this.generateCompleteDateRange();

      // 使用表格数据映射
      const dataMap = new Map();
      this.tableData.forEach(item => {
        dataMap.set(item.statisticsTime, item);
      });

      // 提取数据
      const categories = completeDateRange.length > 0 ? completeDateRange : this.tableData.map(item => item.statisticsTime);
      console.log(`总共有${categories.length}个数据点`);

      // 映射数据
      const shareUsageData = categories.map(date => {
        const item = dataMap.get(date);
        return item && item.shareUsage !== '-' ? Number(item.shareUsage) : null;
      });

      const onYearData = categories.map(date => {
        const item = dataMap.get(date);
        return item && item.onYear !== '-' ? Number(item.onYear) : null;
      });

      const chainRateData = categories.map(date => {
        const item = dataMap.get(date);
        return item && item.chainRate !== '-' ? Number(item.chainRate) : null;
      });

      // 获取峰值
      let peak = 0;
      if (this.timePeriod === 1) { // 日
        if (this.energyType === 1) peak = this.peakValue.dayPerSquareElectricityPeakValue;
        else if (this.energyType === 2) peak = this.peakValue.dayPerSquareWaterPeakValue;
        else if (this.energyType === 3) peak = this.peakValue.dayPerSquareGasPeakValue;
      } else if (this.timePeriod === 2) { // 月
        if (this.energyType === 1) peak = this.peakValue.monthPerSquareElectricityPeakValue;
        else if (this.energyType === 2) peak = this.peakValue.monthPerSquareWaterPeakValue;
        else if (this.energyType === 3) peak = this.peakValue.monthPerSquareGasPeakValue;
      } else if (this.timePeriod === 3) { // 年
        if (this.energyType === 1) peak = this.peakValue.yearPerSquareElectricityPeakValue;
        else if (this.energyType === 2) peak = this.peakValue.yearPerSquareWaterPeakValue;
        else if (this.energyType === 3) peak = this.peakValue.yearPerSquareGasPeakValue;
      }

      // 设置图表数据
      this.chartData = {
        categories: categories,
        series: [
          {
            name: '均摊用量',
            data: shareUsageData,
            type: 'column',
            index: 0
          },
          {
            name: '同比',
            data: onYearData,
            type: 'line',
            format: (val) => val === null ? '-' : `${val}%`,
            index: 1
          },
          {
            name: '环比',
            data: chainRateData,
            type: 'line',
            format: (val) => val === null ? '-' : `${val}%`,
            index: 1
          },
          {
            name: `峰值: ${peak}`,
            data: new Array(categories.length).fill(peak),
            type: 'line',
            style: 'dash',
            color: this.themeColors.peak,
            width: 2,
            index: 0
          }
        ]
      };

      // 根据数据量调整柱宽
      const columnWidth = categories.length > 30 ? 5 : (categories.length > 15 ? 10 : 20);
      
      // 处理超出峰值的柱状图颜色
      const customColors = shareUsageData.map(val => {
        return val > peak ? '#ff4d4f' : '#409EFF';
      });

      // 更新图表配置
      this.chartOpts.extra = {
        ...this.chartOpts.extra,
        column: {
          width: columnWidth,
          barBorderRadius: [6, 6, 0, 0],
          customColor: customColors,
          barGap: '40%' // 增加柱间距
        },
        tooltip: {
          showBox: true,
          showArrow: true,
          showCategory: true,
          borderWidth: 0,
          borderRadius: 4,
          borderColor: '#000000',
          borderOpacity: 0.7,
          bgColor: '#000000',
          bgOpacity: 0.7,
          gridType: 'solid',
          dashLength: 4,
          gridColor: '#CCCCCC',
          fontColor: '#FFFFFF',
          splitLine: true,
          horizentalLine: true,
          xAxisLabel: true,
          yAxisLabel: true,
          labelBgColor: '#FFFFFF',
          labelBgOpacity: 0.7,
          labelFontColor: '#666666'
        }
      };
      
      // 关键配置：启用滚动
      this.chartOpts.enableScroll = true;
      this.chartOpts.touchMoveLimit = 60; // 增加触摸检测灵敏度
      
      // 计算适合的X轴日期间隔并设置
      let dateInterval = 1; // 默认每个日期都显示
      let rotateAngle = 45; // 默认旋转角度
      let fontSize = 12; // 默认字体大小
      
      // 根据数据点数量动态调整日期间隔和显示策略
      if (categories.length > 30) {
        dateInterval = 6; // 每6个日期显示一个
        rotateAngle = 75; // 进一步增大旋转角度
        fontSize = 10; // 缩小字体
      } else if (categories.length > 20) {
        dateInterval = 5; // 每5个日期显示一个
        rotateAngle = 70;
        fontSize = 11;
      } else if (categories.length > 10) {
        dateInterval = 3; // 每3个日期显示一个
        rotateAngle = 60;
      } else {
        dateInterval = 2; // 即使数据少也至少每2个显示一个
        rotateAngle = 45;
      }
      
      // 设置X轴配置
      this.chartOpts.xAxis = {
        disableGrid: true,
        boundaryGap: true,
        axisLine: true,
        axisLineColor: '#CCCCCC',
        labelRotate: rotateAngle,
        fontSize: fontSize,
        margin: 25, // 进一步增大标签边距
        itemCount: Math.ceil(categories.length / dateInterval / 1.5), // 减少显示的标签数量
        scrollShow: true, // 显示滚动条
        scrollAlign: 'left', // 滚动对齐方式
        scrollBackgroundColor: '#F5F5F5', // 滚动条背景色
        scrollColor: '#409EFF', // 滚动条颜色
        format: (val, index) => {
          // 根据计算的间隔显示日期，在间隔点显示完整日期，其他位置留空
          if (index % dateInterval !== 0) {
            return '';
          }
          
          // 针对不同的时间周期格式化日期显示
          if (this.timePeriod === 1) { // 日
            const parts = val.split('-');
            if (parts.length === 3) {
              return `${parts[1]}-${parts[2]}`; // 只显示月-日
            }
          } else if (this.timePeriod === 2) { // 月
            const parts = val.split('-');
            if (parts.length === 2) {
              return `${parts[0].slice(-2)}-${parts[1]}`; // 年末两位-月
            }
          }
          return val;
        }
      };

      // 对于qiun-data-charts，数据更新通过响应式的chartData和chartOpts自动完成
      console.log('图表数据已更新，chartData和chartOpts将自动应用到图表组件');
    },

    /**
     * 生成完整日期范围
     * 根据开始和结束日期，生成连续的日期数组
     * @returns {Array} 连续日期数组
     */
    generateCompleteDateRange() {
      if (!this.dateRange || this.dateRange.length !== 2) {
        return [];
      }

      let startDate, endDate;
      if (this.timePeriod === 1) { // 日
        startDate = new Date(this.dateRange[0]);
        endDate = new Date(this.dateRange[1]);
      } else if (this.timePeriod === 2) { // 月
        const [startYear, startMonth] = this.dateRange[0].split('-');
        const [endYear, endMonth] = this.dateRange[1].split('-');
        startDate = new Date(startYear, parseInt(startMonth) - 1, 1);
        endDate = new Date(endYear, parseInt(endMonth), 0);
      } else if (this.timePeriod === 3) { // 年
        const startYear = this.dateRange[0].split('-')[0];
        const endYear = this.dateRange[1].split('-')[0];
        startDate = new Date(startYear, 0, 1);
        endDate = new Date(endYear, 11, 31);
      } else {
        return [];
      }

      const dates = [];
      const tempDate = new Date(startDate);

      if (this.timePeriod === 1) { // 日
        // 每天
        while (tempDate <= endDate) {
          dates.push(tempDate.toISOString().split('T')[0]);
          tempDate.setDate(tempDate.getDate() + 1);
        }
      } else if (this.timePeriod === 2) { // 月
        // 每月
        while (tempDate <= endDate) {
          const year = tempDate.getFullYear();
          const month = String(tempDate.getMonth() + 1).padStart(2, '0');
          dates.push(`${year}-${month}`);
          tempDate.setMonth(tempDate.getMonth() + 1);
        }
      } else if (this.timePeriod === 3) { // 年
        // 每年
        while (tempDate <= endDate) {
          dates.push(String(tempDate.getFullYear()));
          tempDate.setFullYear(tempDate.getFullYear() + 1);
        }
      }

      return dates;
    },

    /**
     * u5e94u7528Xu8f74u6807u7b7eu8fc1u79fbu7b56u7565 - u5728u6570u636eu8fc7u591au65f6u907fu514du6807u7b7eu91cdu53e0
     * u6839u636eu6570u636eu70b9u6570u91cfu667au80fdu51b3u5b9au6807u7b7eu663eu793au9891u7387
     */
    applyXAxisLabelMigration() {
      // u83b7u53d6u6570u636eu70b9u6570u91cf
      const categoryCount = this.chartData.categories?.length || 0;

      // u6570u636eu70b9u592au5c11u4e0du9700u8981u5904u7406
      if (categoryCount <= 10) {
        // u5c11u4e8e10u4e2au6570u636eu70b9u65f6u663eu793au6240u6709u6807u7b7e
        this.chartOpts.xAxis.labelCount = categoryCount;
        return;
      }

      console.log(`u5e94u7528Xu8f74u6807u7b7eu8fc1u79fbu7b56u7565uff0cu5904u7406${categoryCount}u4e2au6570u636eu70b9`);

      // u6839u636eu6570u636eu91cfu8ba1u7b97u5408u9002u7684u6807u7b7eu95f4u9694
      let interval;

      if (categoryCount > 50) {
        // u6570u636eu91cfu6781u5927u65f6uff0cu663eu793au7684u6807u7b7eu6570u91cfu63a7u5236u572810u4e2au5de6u53f3
        interval = Math.ceil(categoryCount / 10);
      } else if (categoryCount > 30) {
        // u6570u636eu91cfu8f83u5927u65f6uff0cu6bcf3-5u4e2au70b9u663eu793au4e00u4e2au6807u7b7e
        interval = Math.ceil(categoryCount / 15);
      } else if (categoryCount > 20) {
        // u6570u636eu91cfu4e2du7b49u65f6uff0cu6bcf2-3u4e2au70b9u663eu793au4e00u4e2au6807u7b7e
        interval = 2;
      } else {
        // u6570u636eu91cfu7a0du5927u65f6uff0cu4fddu6301u5168u90e8u663eu793au4f46u589eu5927u65cbu8f6cu89d2u5ea6
        interval = 1;
        this.chartOpts.xAxis.labelRotate = 60; // u589eu5927u65cbu8f6cu89d2u5ea6
      }

      console.log(`u8bbeu7f6eXu8f74u6807u7b7eu95f4u9694u4e3a${interval}uff0cu65cbu8f6cu89d2u5ea6u4e3a${this.chartOpts.xAxis.labelRotate}u5ea6`);

      // u5b9eu73b0u6807u7b7eu7b5bu9009u903bu8f91 - u4f7fu7528formatteru51fdu6570
      const originalFormatter = this.chartOpts.xAxis.format;

      // u66ffu6362u4e3au5e26u95f4u9694u7684formatter
      this.chartOpts.xAxis.format = (val, index) => {
        // u6bcfintervalu4e2au663eu793au4e00u4e2au6807u7b7euff0cu5176u4ed6u8fd4u56deu7a7au5b57u7b26u4e32
        if (index % interval !== 0) {
          return '';
        }

        // u8c03u7528u539fu6709u7684formatteru5904u7406u8981u663eu793au7684u6807u7b7e
        return originalFormatter(val);
      };
    },

    /**
     * u5e94u7528u4e3bu9898u989cu8272u5230u56feu8868u4e2d
     * u6839u636eu5f53u524du7cfbu7edfu4e3bu9898u8bbeu7f6eu56feu8868u7684u989cu8272u548cu6837u5f0f
     */
    applyThemeColors() {
      try {
        console.log('u5e94u7528u4e3bu9898u989cu8272u5230u56feu8868u4e2d');

        // u6d4bu8bd5u7cfbu7edfu4e3bu9898
        const systemInfo = uni.getSystemInfoSync();
        if (systemInfo.theme) {
          console.log('u68c0u6d4bu5230u7cfbu7edfu4e3bu9898:', systemInfo.theme);

          // u5982u679cu7cfbu7edfu4e3bu9898u662fu6697u8272u6a21u5f0fuff0cu8c03u6574u989cu8272
          if (systemInfo.theme === 'dark') {
            this.themeColors = {
              primary: '#79bbff',    // u6d45u8272
              success: '#95d475',    // u6210u529fu8272
              warning: '#f3d19e',    // u8b66u544au8272
              danger: '#fab6b6',     // u5371u9669u8272
              peak: '#ffcc80',       // u5cf0u503cu8272
              up: '#fab6b6',         // u4e0au5347u8272
              down: '#95d475',       // u4e0bu964du8272
              background: '#1e1e1e'   // u80ccu666fu8272
            };
          }
        }

        // u66f4u65b0u56feu8868u914du7f6e
        if (this.chartOpts) {
          // u8bbeu7f6eu56feu8868u989cu8272
          this.chartOpts.color = [
            this.themeColors.primary,   // u4e3bu8272
            this.themeColors.success,   // u6210u529fu8272
            this.themeColors.warning,   // u8b66u544au8272
            this.themeColors.peak       // u5cf0u503cu8272
          ];

          // u8bbeu7f6eu56feu8868u80ccu666fu8272
          this.chartOpts.background = this.themeColors.background;

          // u66f4u65b0u56feu4f8bu683cu5f0f
          this.chartOpts.legend.formatter = (name) => {
            // u5cf0u503cu7ebfu4f7fu7528u7279u6b8au989cu8272
            if (name.includes('u5cf0u503c')) {
              return {
                text: name,
                color: this.themeColors.peak,
                lineDash: [5, 5]
              };
            }

            // u5747u6458u7528u91cfu8d85u8fc7u5cf0u503cu65f6u4f7fu7528u5371u9669u8272
            if (name === 'u5747u6458u7528u91cf') {
              return [
                { text: name, color: this.themeColors.primary },
                { text: 'u8d85u51fau5cf0u503c', color: this.themeColors.danger }
              ];
            }

            // u540cu6bd4u548cu73afu6bd4u4f7fu7528u6210u529fu8272
            if (name === 'u540cu6bd4' || name === 'u73afu6bd4') {
              return { text: name, color: name === 'u540cu6bd4' ? this.themeColors.success : this.themeColors.warning };
            }

            return name;
          };
        }

        console.log('u4e3bu9898u989cu8272u5e94u7528u5b8cu6210');
      } catch (e) {
        console.error('u5e94u7528u4e3bu9898u989cu8272u65f6u51fau9519:', e);
      }
    },

    /**
     * u66f4u65b0u56feu8868u6837u5f0f
     * u6839u636eu5f53u524du7cfbu7edfu4e3bu9898u8c03u6574u56feu8868u7684u6837u5f0f
     */
    updateChartStyles() {
      try {
        // u68c0u67e5u56feu8868u5b9eu4f8bu662fu5426u5b58u5728
        if (!this.chartInstance || !this.chartData || !this.chartData.series) {
          return;
        }

        // u83b7u53d6u5cf0u503cu7ebfu6570u636e
        const peakSeries = this.chartData.series.find(s => s.name.includes('u5cf0u503c'));
        if (!peakSeries) {
          return;
        }

        // u83b7u53d6u5747u6458u7528u91cfu6570u636e
        const usageSeries = this.chartData.series.find(s => s.name === 'u5747u6458u7528u91cf');
        if (!usageSeries || !usageSeries.data) {
          return;
        }

        // u83b7u53d6u5cf0u503c
        const peakValue = peakSeries.data[0]; // u5cf0u503cu6570u636eu7684u7b2cu4e00u9879

        // u6839u636eu5cf0u503cu8c03u6574u5747u6458u7528u91cfu989cu8272
        const customColors = [];
        usageSeries.data.forEach((val, index) => {
          if (val > peakValue) {
            customColors[index] = this.themeColors.danger; // u8d85u8fc7u5cf0u503cu65f6u4f7fu7528u5371u9669u8272
          } else {
            customColors[index] = this.themeColors.primary; // u672au8d85u8fc7u5cf0u503cu65f6u4f7fu7528u4e3bu8272
          }
        });

        // u5e94u7528u81eau5b9au4e49u989cu8272
        if (this.chartOpts.extra && this.chartOpts.extra.column) {
          this.chartOpts.extra.column.customColor = customColors;
        } else if (this.chartOpts.extra) {
          this.chartOpts.extra.column = { customColor: customColors };
        } else {
          this.chartOpts.extra = { column: { customColor: customColors } };
        }

        // 图表样式更新通过响应式的chartOpts自动完成
        console.log('图表样式配置已更新，将自动应用到图表组件');

        console.log('u56feu8868u6837u5f0fu66f4u65b0u5b8cu6210');
      } catch (e) {
        console.error('u66f4u65b0u56feu8868u6837u5f0fu65f6u51fau9519:', e);
      }
    },
  }
}
</script>

<style lang="scss">
/*
 * 医院能耗峰值监控页面样式
 * 包含筛选区域、图表容器和数据表格样式
 */

.container {
  padding: 20rpx;
  background-color: #f5f5f5;
  min-height: 100vh;

  /* 卡片通用样式 - 简单美化 */
  .card {
    background: white;
    border-radius: 16rpx;
    padding: 24rpx;
    margin-bottom: 20rpx;
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
    border: 1rpx solid #f0f0f0;
  }

  /* 筛选区域样式 - 简单美化 */
  .filter-section {
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    background: #fafbfc;
    border-radius: 12rpx;
    padding: 16rpx;

    /* 垂直筛选列样式 */
    .filter-column {
      display: flex;
      flex-direction: column;
      gap: 16rpx;

      /* 单个筛选项样式 - 简单美化 */
      .filter-item {
        display: flex;
        align-items: center;
        width: 100%;
        padding: 16rpx;
        background: white;
        border-radius: 10rpx;
        border: 1rpx solid #e9ecef;
        box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
        margin-bottom: 12rpx;

        /* 筛选标签 */
        .filter-label {
          min-width: 150rpx;
          font-size: 30rpx;
          color: #495057;
          font-weight: 600;
        }

        /* 能耗类型选择器 */
        .energy-select {
          width: 300rpx;
          height: 72rpx;
          line-height: 72rpx;
          border: 1rpx solid #e9ecef;
          border-radius: 10rpx;
          background: white;
          font-size: 28rpx;
          color: #495057;
          padding: 0 12rpx;
          box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);
          transition: all 0.3s ease;

          &:focus {
            border-color: #409EFF;
            box-shadow: 0 0 0 3rpx rgba(64, 158, 255, 0.1);
          }
        }

        /* 时间周期分段控制器 - 简单美化 */
        .segmented-control {
          display: flex;
          background-color: #f8f9fa;
          border-radius: 12rpx;
          overflow: hidden;
          border: 1rpx solid #e9ecef;
          box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);

          .segment {
            flex: 1;
            padding: 16rpx 32rpx;
            text-align: center;
            font-size: 28rpx;
            font-weight: 500;
            transition: all 0.3s ease;
            cursor: pointer;

            &.active {
              background: linear-gradient(135deg, #409EFF 0%, #5dade2 100%);
              color: white;
              box-shadow: 0 2rpx 6rpx rgba(64, 158, 255, 0.3);
              transform: translateY(-1rpx);
            }

            &:not(.active) {
              color: #666;

              &:hover {
                background-color: #e8f4fd;
                color: #409EFF;
              }
            }
          }
        }

        /* 日期筛选区域 - 简单美化 */
        &.date-filter {
          margin-top: 20rpx;
          padding: 20rpx;
          background: #fafbfc;
          border-radius: 12rpx;
          border: 1rpx solid #e9ecef;

          .filter-label {
            margin-bottom: 12rpx;
            color: #495057;
            font-weight: 600;
          }

          .date-select-container {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: 20rpx;

            /* 日期范围选择器 */
            .date-range-container {
              display: flex;
              align-items: center;
              width: 100%;
              background: white;
              border-radius: 10rpx;
              padding: 12rpx;
              border: 1rpx solid #e9ecef;
              box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.04);

              .date-picker {
                flex: 1;
                border: none;
                border-radius: 6rpx;
                padding: 8rpx 12rpx;
                background: #f8f9fa;
                font-size: 26rpx;
                color: #495057;
              }

              .date-separator {
                margin: 0 12rpx;
                color: #6c757d;
                font-size: 24rpx;
                font-weight: 500;
              }
            }

            /* 快捷日期按钮组 */
            .button-group {
              display: flex;
              gap: 12rpx;
              margin-top: 8rpx;
              flex-wrap: wrap;

              .quick-button {
                font-size: 24rpx;
                padding: 0 20rpx;
                height: 60rpx;
                line-height: 60rpx;
                margin: 0;
                background: linear-gradient(135deg, #409EFF 0%, #5dade2 100%);
                border: none;
                border-radius: 30rpx;
                color: white;
                font-weight: 500;
                box-shadow: 0 2rpx 8rpx rgba(64, 158, 255, 0.2);
                transition: all 0.3s ease;

                &:hover {
                  transform: translateY(-1rpx);
                  box-shadow: 0 4rpx 12rpx rgba(64, 158, 255, 0.3);
                }
              }
            }
          }
        }
      }
    }
  }

  /* 图表容器样式 */
  .chart-container {
    width: 100%;
    height: 800rpx;
    position: relative;
    margin: 0 auto;
    overflow: hidden;

    /* 无数据显示状态 */
    .no-data {
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 32rpx;
      color: #999;
      background: rgba(255,255,255,0.8);
      z-index: 2;
    }
  }

  /* 表格标题样式 */
  .table-title {
    font-size: 32rpx;
    font-weight: bold;
    text-align: center;
    margin-bottom: 30rpx;
    color: #333;
  }

  /* 数据表格样式 */
  .table {
    width: 100%;
    background: #fff;
    border-radius: 8rpx;
    overflow: hidden;
    border: 1px solid #f0f0f0;

    /* 表头样式 */
    .table-header {
      display: flex;
      background-color: #f8f9fa;
      position: sticky;
      top: 0;
      z-index: 1;

      .th {
        flex: 1;
        padding: 24rpx 12rpx;
        text-align: center;
        font-weight: 500;
        font-size: 28rpx;
        color: #333;
        white-space: nowrap;
      }
    }

    /* 表格内容区域 */
    .table-body {
      .tr {
        display: flex;
        border-bottom: 1px solid #f0f0f0;
        transition: background-color 0.3s;

        &:active {
          background-color: #f5f5f5;
        }

        .td {
          flex: 1;
          padding: 24rpx 12rpx;
          text-align: center;
          font-size: 26rpx;
          color: #333;
          white-space: nowrap;

          /* 升降指标样式 */
          &.up {
            color: #f56c6c; /* 红色表示上升 */
          }

          &.down {
            color: #67c23a; /* 绿色表示下降 */
          }
        }
      }
    }
  }

  /* 分页控件样式 */
  .pagination {
    margin-top: 24rpx;
    display: flex;
    justify-content: center;
    padding: 20rpx 0;
  }

  /* 自定义时间选择器样式 */
  .custom-date-picker {
    width: 100%;
    margin-bottom: 20rpx;
  }

  /* 日期选择容器样式 */
  .date-range-container {
    margin-bottom: 20rpx;
  }

  /* 快速按钮组样式 */
  .button-group {
    display: flex;
    gap: 20rpx;
    flex-wrap: wrap;
    justify-content: flex-start;

    .quick-button {
      flex: none;
      min-width: 120rpx;
      height: 60rpx;
      font-size: 24rpx;
      border-radius: 8rpx;
    }
  }
}
</style>
